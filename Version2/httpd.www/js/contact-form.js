document.addEventListener('DOMContentLoaded', function() {
  const contactForm = document.getElementById('contact-form');

  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // Get form values
      const name = document.getElementById('name').value;
      const company = document.getElementById('company').value;
      const email = document.getElementById('email').value;
      const phone = document.getElementById('phone').value;
      const subject = document.getElementById('subject').value;
      const message = document.getElementById('message').value;
      const privacy = document.getElementById('privacy').checked;

      // Simple validation
      if (!name || !email || !subject || !message || !privacy) {
        alert('Bitte füllen Sie alle Pflichtfelder aus.');
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        alert('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
        return;
      }

      // Disable the submit button and show loading state
      const submitButton = contactForm.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.textContent;
      submitButton.disabled = true;
      submitButton.textContent = 'Wird gesendet...';

      // Collect form data
      const formData = {
        name,
        company,
        email,
        phone,
        subject,
        message,
        timestamp: new Date().toISOString(),
        processed: false
      };

      // Store in localStorage for admin dashboard
      let contactForms = JSON.parse(localStorage.getItem('contactForms')) || [];
      contactForms.push(formData);
      localStorage.setItem('contactForms', JSON.stringify(contactForms));

      // Send <NAME_EMAIL>
      const emailData = new FormData();
      emailData.append('form_type', 'contact');
      emailData.append('form_data', JSON.stringify(formData));
      emailData.append('website', ''); // Honeypot field

      // Send the data to the server
      fetch('send_form.php', {
        method: 'POST',
        body: emailData
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        console.log('Email sent:', data);

        // Create a success message
        const successMessage = document.createElement('div');
        successMessage.className = 'form-success-message';
        successMessage.innerHTML = `
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h3>Vielen Dank für Ihre Nachricht!</h3>
          <p>Wir haben Ihre Anfrage erhalten und werden uns schnellstmöglich bei Ihnen melden.</p>
        `;

        // Replace the form with the success message
        contactForm.style.opacity = '0';
        setTimeout(function() {
          contactForm.parentNode.replaceChild(successMessage, contactForm);
          successMessage.style.opacity = '0';
          setTimeout(function() {
            successMessage.style.opacity = '1';
          }, 50);
        }, 300);
      })
      .catch(error => {
        console.error('Error sending email:', error);

        // Reset button state
        submitButton.disabled = false;
        submitButton.textContent = originalButtonText;

        // Show error message
        const errorMessage = document.createElement('div');
        errorMessage.className = 'form-error-message';
        errorMessage.style.cssText = `
          background-color: #fee;
          border: 1px solid #fcc;
          color: #c33;
          padding: 15px;
          border-radius: 5px;
          margin-top: 15px;
          text-align: center;
        `;
        errorMessage.innerHTML = `
          <div style="margin-bottom: 10px;">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h4 style="margin: 0 0 10px 0;">Fehler beim Senden</h4>
          <p style="margin: 0;">Es gab ein Problem beim Senden Ihrer Nachricht. Bitte versuchen Sie es erneut oder kontaktieren Sie uns direkt unter <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
        `;

        // Remove any existing error messages
        const existingError = contactForm.querySelector('.form-error-message');
        if (existingError) {
          existingError.remove();
        }

        // Add error message to form
        contactForm.appendChild(errorMessage);

        // Add mailto fallback button
        const mailtoButton = document.createElement('button');
        mailtoButton.type = 'button';
        mailtoButton.className = 'btn btn-outline';
        mailtoButton.style.marginTop = '10px';
        mailtoButton.innerHTML = '<i class="fas fa-envelope"></i> E-Mail direkt senden';
        mailtoButton.onclick = function() {
          const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(
            `Name: ${name}\n` +
            `Firma: ${company}\n` +
            `E-Mail: ${email}\n` +
            `Telefon: ${phone}\n\n` +
            `Nachricht:\n${message}`
          )}`;
          window.location.href = mailtoLink;
        };
        errorMessage.appendChild(mailtoButton);

        // Scroll to error message
        errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
      });
    });
  }
});
