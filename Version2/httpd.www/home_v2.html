<!doctype html>
<html lang="de">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Heart & Soul Medienproduktion - Alternative Homepage</title>
  <link rel="stylesheet" href="css/style_fixed.css">
  <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.css">
  <link rel="stylesheet" href="css/locomotive-scroll-basic.css">
  <link rel="stylesheet" href="css/plyr-custom.css">
  <link rel="stylesheet" href="css/animations.css">
  <meta name="description" content="Heart & Soul Medienproduktion - Alternative Homepage Design für professionelle Videoproduktion.">

  <link rel="icon" href="favicon.png" type="image/png">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" sizes="180x180" href="favicon.png">
  <link rel="manifest" href="site.webmanifest">
  <meta name="theme-color" content="#121212">

  <style>
    /* Alternative Homepage Styles */
    .services-showcase {
      padding: 8rem 0;
      background: linear-gradient(135deg, rgba(18, 18, 18, 0.95), rgba(30, 30, 30, 0.9));
    }

    .services-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 3rem;
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .service-card {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 20px;
      padding: 3rem;
      text-align: center;
      transition: all 0.4s ease;
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
    }

    .service-card:hover {
      transform: translateY(-10px);
      background: rgba(255, 255, 255, 0.05);
      border-color: var(--primary-color);
      box-shadow: 0 20px 40px rgba(255, 48, 48, 0.1);
    }

    .service-icon {
      font-size: 4rem;
      color: var(--primary-color);
      margin-bottom: 2rem;
      display: block;
    }

    .service-card h3 {
      font-size: 1.8rem;
      font-weight: 700;
      color: var(--text-light);
      margin-bottom: 1.5rem;
    }

    .service-card p {
      color: var(--text-gray);
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    .stats-section {
      padding: 6rem 0;
      background: rgba(255, 48, 48, 0.05);
      text-align: center;
    }

    .stats-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 4rem;
      margin-top: 4rem;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: 4rem;
      font-weight: 900;
      color: var(--primary-color);
      display: block;
      margin-bottom: 1rem;
    }

    .stat-label {
      font-size: 1.2rem;
      color: var(--text-light);
      font-weight: 600;
    }

    .process-section {
      padding: 8rem 0;
      background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(18, 18, 18, 0.9));
    }

    .process-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .process-steps {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 3rem;
      margin-top: 4rem;
    }

    .process-step {
      position: relative;
      text-align: center;
      padding: 2rem;
    }

    .step-number {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, var(--primary-color), #ff5050);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      font-weight: 900;
      color: white;
      margin: 0 auto 2rem auto;
    }

    .process-step h3 {
      font-size: 1.6rem;
      font-weight: 700;
      color: var(--text-light);
      margin-bottom: 1rem;
    }

    .process-step p {
      color: var(--text-gray);
      line-height: 1.6;
    }

    .testimonial-section {
      padding: 6rem 0;
      background: rgba(255, 255, 255, 0.02);
    }

    .testimonial-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 2rem;
      text-align: center;
    }

    .testimonial-card {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 20px;
      padding: 4rem;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .testimonial-text {
      font-size: 1.4rem;
      font-style: italic;
      color: var(--text-light);
      line-height: 1.8;
      margin-bottom: 2rem;
    }

    .testimonial-author {
      font-size: 1.1rem;
      color: var(--primary-color);
      font-weight: 600;
    }

    .cta-section-v2 {
      padding: 8rem 0;
      background: linear-gradient(135deg, rgba(255, 48, 48, 0.1), rgba(255, 48, 48, 0.05));
      text-align: center;
    }

    .cta-container-v2 {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .cta-title-v2 {
      font-size: 3rem;
      font-weight: 900;
      color: var(--text-light);
      margin-bottom: 2rem;
      line-height: 1.2;
    }

    .cta-subtitle-v2 {
      font-size: 1.3rem;
      color: var(--text-gray);
      margin-bottom: 3rem;
      line-height: 1.6;
    }

    .cta-buttons-v2 {
      display: flex;
      gap: 2rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    @media (max-width: 768px) {
      .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
      }

      .service-card {
        padding: 2rem;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
      }

      .process-steps {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .testimonial-card {
        padding: 2rem;
      }

      .cta-title-v2 {
        font-size: 2.2rem;
      }

      .cta-buttons-v2 {
        flex-direction: column;
        align-items: center;
      }
    }

    @media (max-width: 576px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }

      .stat-number {
        font-size: 3rem;
      }
    }
  </style>
</head>

<body data-scroll-container>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <a href="index.html"><img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
      </div>
      <div class="nav-links">
        <a href="index.html">Home</a>
        <a href="portfolio.html">Portfolio</a>
        <a href="leistungen.html">Leistungen</a>
        <a href="kontakt.html">Kontakt</a>
      </div>
      <button class="mobile-menu-btn">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>
  <!-- Mobile Menu -->
  <div class="mobile-menu">
    <a href="index.html">Home</a>
    <a href="portfolio.html">Portfolio</a>
    <a href="leistungen.html">Leistungen</a>
    <a href="kontakt.html">Kontakt</a>
  </div>

  <!-- Hero Video Section (Same as original) -->
  <section class="hero-video-section" data-scroll-section>
    <div class="hero-container">
      <video autoplay muted loop playsinline class="hero-video" preload="auto">
        <source src="videos/Heart-and-Soul-Hero.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      <div class="hero-overlay">
        <h1>Heart & Soul <span style="color: var(--primary-color);">Film- und Medienproduktion</span></h1>
        <p>Visueller Content, der wirkt. Ergebnisse, die zählen.</p>
      </div>
    </div>
  </section>

  <!-- Services Showcase Section -->
  <section class="services-showcase" data-scroll-section>
    <div class="services-grid">
      <div class="service-card">
        <i class="fas fa-video service-icon"></i>
        <h3>Werbefilme</h3>
        <p>Überzeugende Werbefilme, die Ihre Zielgruppe erreichen und zum Handeln bewegen. Von Social Media bis TV-Spot.</p>
        <a href="werbefilm.html" class="btn btn-outline">Mehr erfahren</a>
      </div>
      
      <div class="service-card">
        <i class="fas fa-building service-icon"></i>
        <h3>Imagefilme</h3>
        <p>Authentische Unternehmensfilme, die Ihre Werte und Vision emotional transportieren und Vertrauen schaffen.</p>
        <a href="imagefilm.html" class="btn btn-outline">Mehr erfahren</a>
      </div>
      
      <div class="service-card">
        <i class="fas fa-users service-icon"></i>
        <h3>Recruiting Videos</h3>
        <p>Gewinnen Sie Top-Talente mit authentischen Recruiting-Videos, die Ihre Unternehmenskultur zeigen.</p>
        <a href="recruiting.html" class="btn btn-outline">Mehr erfahren</a>
      </div>
      
      <div class="service-card">
        <i class="fas fa-calendar-alt service-icon"></i>
        <h3>Eventfilme</h3>
        <p>Halten Sie besondere Momente fest und schaffen Sie bleibende Erinnerungen an Ihre wichtigsten Events.</p>
        <a href="eventfilm.html" class="btn btn-outline">Mehr erfahren</a>
      </div>
      
      <div class="service-card">
        <i class="fas fa-film service-icon"></i>
        <h3>Dokumentarfilme</h3>
        <p>Erzählen Sie Ihre Geschichte authentisch und tiefgreifend mit professionellen Dokumentarfilmen.</p>
        <a href="dokumentarfilm.html" class="btn btn-outline">Mehr erfahren</a>
      </div>
      
      <div class="service-card">
        <i class="fas fa-tv service-icon"></i>
        <h3>Corporate TV</h3>
        <p>Professionelle Unternehmenskommunikation durch hochwertige Corporate TV Produktionen.</p>
        <a href="corporate-tv.html" class="btn btn-outline">Mehr erfahren</a>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="stats-section" data-scroll-section>
    <div class="stats-container">
      <h2 class="section-title">Zahlen, die für sich sprechen</h2>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-number">150+</span>
          <span class="stat-label">Erfolgreiche Projekte</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">8+</span>
          <span class="stat-label">Jahre Erfahrung</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">50+</span>
          <span class="stat-label">Zufriedene Kunden</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">24h</span>
          <span class="stat-label">Antwortzeit</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Process Section -->
  <section class="process-section" data-scroll-section>
    <div class="process-container">
      <h2 class="section-title">Unser <span style="color: var(--primary-color);">Prozess</span></h2>
      <div class="process-steps">
        <div class="process-step">
          <div class="step-number">1</div>
          <h3>Beratung & Konzept</h3>
          <p>Wir verstehen Ihre Ziele und entwickeln gemeinsam das perfekte Konzept für Ihren Film.</p>
        </div>
        <div class="process-step">
          <div class="step-number">2</div>
          <h3>Planung & Vorbereitung</h3>
          <p>Detaillierte Planung aller Produktionsschritte für einen reibungslosen Ablauf.</p>
        </div>
        <div class="process-step">
          <div class="step-number">3</div>
          <h3>Produktion</h3>
          <p>Professionelle Umsetzung mit modernster Technik und erfahrenem Team.</p>
        </div>
        <div class="process-step">
          <div class="step-number">4</div>
          <h3>Post-Produktion</h3>
          <p>Schnitt, Farbkorrektur und Vertonung für das perfekte Endergebnis.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonial Section -->
  <section class="testimonial-section" data-scroll-section>
    <div class="testimonial-container">
      <h2 class="section-title">Was unsere <span style="color: var(--primary-color);">Kunden</span> sagen</h2>
      <div class="testimonial-card">
        <p class="testimonial-text">"Heart & Soul hat unsere Erwartungen übertroffen. Der Imagefilm spiegelt perfekt wider, wer wir als Unternehmen sind. Professionell, kreativ und absolut zuverlässig."</p>
        <div class="testimonial-author">— Sarah M., Marketing Direktorin</div>
      </div>
    </div>
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section-v2" data-scroll-section>
    <div class="cta-container-v2">
      <h2 class="cta-title-v2">Bereit für Ihr <span style="color: var(--primary-color);">nächstes Projekt?</span></h2>
      <p class="cta-subtitle-v2">Lassen Sie uns gemeinsam Ihre Vision zum Leben erwecken. Kontaktieren Sie uns für ein kostenloses Beratungsgespräch.</p>
      <div class="cta-buttons-v2">
        <a href="meeting.html" class="btn btn-primary btn-large">Kostenloses Gespräch</a>
        <a href="portfolio.html" class="btn btn-outline btn-large">Portfolio ansehen</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer" data-scroll-section>
    <div class="footer-container">
      <div>
        <div class="footer-logo">
          <img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
        </div>
        <p class="footer-description">Professionelle Videoproduktion mit Herz und Seele. Wir bringen Ihre Botschaft zum Leben.</p>
        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
          <a href="https://www.linkedin.com/in/malte-strömsdörfer-998b55112" class="social-link" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
      <div>
        <h3 class="footer-title">Leistungen</h3>
        <ul class="footer-links">
          <li><a href="werbefilm.html">Werbefilme</a></li>
          <li><a href="imagefilm.html">Imagefilme</a></li>
          <li><a href="recruiting.html">Recruitingfilme</a></li>
          <li><a href="eventfilm.html">Eventfilme</a></li>
          <li><a href="dokumentarfilm.html">Dokumentarfilme</a></li>
          <li><a href="corporate-tv.html">Corporate TV</a></li>
        </ul>
      </div>
      <div>
        <h3 class="footer-title">Kontakt</h3>
        <ul class="footer-links">
          <li><i class="fas fa-phone mr-2"></i> <a href="tel:+4917650200617">+49 (0) 176 50200617</a></li>
          <li><i class="fas fa-envelope mr-2"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        </ul>
      </div>
    </div>
    <div class="copyright">
      <div class="footer-legal">
        <a href="impressum.html">Impressum</a>
        <a href="datenschutz.html">Datenschutz</a>
        <a href="agb.html">AGB</a>
      </div>
      <p>&copy; 2025 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.</p>
    </div>
  </footer>

  <!-- Cookie Consent -->
  <div id="cookie-consent" class="cookie-consent">
    <div class="cookie-content">
      <p>Wir verwenden Cookies, um Ihnen die bestmögliche Erfahrung auf unserer Website zu bieten.</p>
      <div class="cookie-buttons">
        <button id="accept-cookies" class="btn btn-primary">Akzeptieren</button>
        <button id="decline-cookies" class="btn btn-outline">Ablehnen</button>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
  <script src="js/app.js"></script>
  <script src="js/smooth-scroll-basic.js"></script>
  <script src="js/cookie-consent.js"></script>
  <!-- WhatsApp Floating Button -->
  <a href="https://wa.me/4917650200617" class="whatsapp-float" target="_blank" rel="noopener noreferrer">
    <i class="fab fa-whatsapp"></i>
    <span>WhatsApp</span>
  </a>

</body>

</html>
