document.addEventListener('DOMContentLoaded', function() {
  console.log('Portfolio.js loaded');
  let players = [];

  // Check if videos exist
  const portfolioVideos = document.querySelectorAll('.portfolio-video video');
  console.log('Found', portfolioVideos.length, 'portfolio videos');

  portfolioVideos.forEach((video, index) => {
    const source = video.querySelector('source');
    console.log(`Video ${index + 1}:`, source ? source.src : 'no source');
  });

  // Lazy load videos when they come into view
  const videoObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const video = entry.target;
        const source = video.querySelector('source');
        console.log('Video entering viewport:', source ? source.src : 'no source');

        // Initialize Plyr only when video comes into view
        if (!video.dataset.initialized) {
          console.log('Initializing Plyr for video:', source ? source.src : 'no source');

          const player = new Plyr(video, {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            loadSprite: true,
            iconUrl: 'https://cdn.plyr.io/3.7.8/plyr.svg',
            blankVideo: 'https://cdn.plyr.io/static/blank.mp4',
            autoplay: false,
            autopause: true,
            seekTime: 10,
            volume: 1,
            muted: false,
            clickToPlay: true,
            disableContextMenu: false,
            hideControls: false,
            resetOnEnd: true,
            keyboard: { focused: true, global: false }
          });

          players.push(player);
          video.dataset.initialized = 'true';

          // Set up pause other videos functionality
          player.on('play', () => {
            players.forEach(otherPlayer => {
              if (otherPlayer !== player && otherPlayer.playing) {
                otherPlayer.pause();
              }
            });
          });

          player.on('ready', () => {
            console.log('Player ready for:', source ? source.src : 'no source');
          });

          player.on('error', (error) => {
            console.error('Player error for:', source ? source.src : 'no source', error);
          });

          // Load video metadata when first observed
          video.addEventListener('loadedmetadata', function() {
            video.currentTime = 0;
            console.log('Video metadata loaded:', source ? source.src : 'no source');
          });

          video.addEventListener('error', function(e) {
            console.error('Video error:', source ? source.src : 'no source', e);
          });
        }

        // Stop observing this video after initialization
        videoObserver.unobserve(video);
      }
    });
  }, {
    rootMargin: '100px' // Start loading 100px before video enters viewport
  });

  // Observe all videos for lazy loading
  portfolioVideos.forEach(video => {
    videoObserver.observe(video);
  });
});
