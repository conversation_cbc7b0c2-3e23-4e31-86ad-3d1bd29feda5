<!DOCTYPE html>
<html lang="de">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Filmkalkulator - Heart & Soul Film- und Medienproduktion</title>
  <link rel="stylesheet" href="css/style_fixed.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="css/form.css">
</head>

<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <a href="index.html"><img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
      </div>
      <div class="nav-links">
        <a href="index.html">Home</a>
        <a href="index.html#videos">Videos</a>
        <a href="leistungen.html">Leistungen</a>
        <a href="kontakt.html">Kontakt</a>
      </div>
      <button class="mobile-menu-btn">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>
  <!-- Mobile Menu (separate from navbar for better structure) -->
  <div class="mobile-menu">
    <a href="index.html">Home</a>
    <a href="index.html#videos">Videos</a>
    <a href="leistungen.html">Leistungen</a>
    <a href="kontakt.html">Kontakt</a>
  </div>

  <!-- Main Content Container -->
  <div class="content-container">
    <!-- Form Section -->
    <section class="form-section">
      <div class="form-container">
        <!-- Progress Indicator -->
        <div class="progress-container">
          <div class="progress-percentage">0%</div>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
        </div>



        <!-- Multi-step Form -->
        <form id="film-calculator" class="multi-step-form">
          <!-- Step 1: Einstieg -->
          <div class="form-step active" id="step-1">
            <div class="step-content">
              <h1 class="form-title">🎬 „Was kostet Ihre Filmproduktion?"</h1>
              <p class="form-subtitle">In 2 Minuten zu einer ersten Einschätzung – starten Sie jetzt kostenlos.</p>

              <div class="form-buttons text-center">
                <button type="button" class="btn btn-primary next-step">Jetzt starten</button>
              </div>
            </div>
          </div>

          <!-- Step 2: Lead erfassen -->
          <div class="form-step" id="step-2">
            <div class="step-content">
              <h2 class="step-title">Bevor wir starten – wie dürfen wir Sie kontaktieren?</h2>
              <div class="form-group">
                <label for="name">👤 Wie ist Ihr Name?</label>
                <input type="text" id="name" name="name" required>
              </div>
              <div class="form-group">
                <label for="company">🏢 Für welches Unternehmen/Firma (optional):</label>
                <input type="text" id="company" name="company">
              </div>
              <div class="form-group">
                <label for="email">✉️ Ihre E-Mail-Adresse:</label>
                <input type="email" id="email" name="email" required>
              </div>
              <div class="form-group">
                <label for="phone">☎️ Telefonnummer (optional):</label>
                <input type="tel" id="phone" name="phone">
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter zur Projektabfrage</button>
              </div>
            </div>
          </div>

          <!-- Step 3: Filmart -->
          <div class="form-step" id="step-3">
            <div class="step-content">
              <h2 class="step-title">Welche Art von Film möchten Sie produzieren?</h2>
              <p class="step-description">Wählen Sie eine oder mehrere Optionen aus.</p>

              <div class="form-group">
                <div class="checkbox-group">
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-1" name="film-type" value="Imagefilm">
                    <label for="film-type-1">Imagefilm</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-2" name="film-type" value="Werbespot">
                    <label for="film-type-2">Werbespot (TV/Online)</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-3" name="film-type" value="Event-Aftermovie">
                    <label for="film-type-3">Event-Aftermovie</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-4" name="film-type" value="Social-Media-Content">
                    <label for="film-type-4">Social-Media-Content</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-5" name="film-type" value="Produktvideo">
                    <label for="film-type-5">Produktvideo</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-6" name="film-type" value="Recruitingfilm">
                    <label for="film-type-6">Recruitingfilm</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-type-7" name="film-type" value="Sonstiges">
                    <label for="film-type-7">Sonstiges:</label>
                    <input type="text" id="film-type-other" name="film-type-other" class="inline-input">
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 4: Filmziel -->
          <div class="form-step" id="step-4">
            <div class="step-content">
              <h2 class="step-title">Was ist das Ziel des Films?</h2>
              <p class="step-description">Wählen Sie eine oder mehrere Optionen aus.</p>

              <div class="form-group">
                <div class="checkbox-group">
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-goal-1" name="film-goal" value="Aufmerksamkeit">
                    <label for="film-goal-1">Aufmerksamkeit erzeugen</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-goal-2" name="film-goal" value="Vertrauen">
                    <label for="film-goal-2">Vertrauen aufbauen</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-goal-3" name="film-goal" value="Verkäufe">
                    <label for="film-goal-3">Verkäufe steigern</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-goal-4" name="film-goal" value="Mitarbeiter">
                    <label for="film-goal-4">Mitarbeiter gewinnen</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-goal-5" name="film-goal" value="Marke">
                    <label for="film-goal-5">Marke emotional aufladen</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-goal-6" name="film-goal" value="Sonstiges">
                    <label for="film-goal-6">Sonstiges:</label>
                    <input type="text" id="film-goal-other" name="film-goal-other" class="inline-input">
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 5: Plattform -->
          <div class="form-step" id="step-5">
            <div class="step-content">
              <h2 class="step-title">Wo soll der Film gezeigt werden?</h2>
              <p class="step-description">Wählen Sie eine oder mehrere Optionen aus.</p>

              <div class="form-group">
                <div class="checkbox-group">
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-1" name="film-platform" value="Website">
                    <label for="film-platform-1">Website</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-2" name="film-platform" value="YouTube">
                    <label for="film-platform-2">YouTube</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-3" name="film-platform" value="Instagram">
                    <label for="film-platform-3">Instagram</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-4" name="film-platform" value="TikTok">
                    <label for="film-platform-4">TikTok</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-5" name="film-platform" value="LinkedIn">
                    <label for="film-platform-5">LinkedIn</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-6" name="film-platform" value="Kino/TV">
                    <label for="film-platform-6">Kino/TV</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-7" name="film-platform" value="Intern">
                    <label for="film-platform-7">Interne Kommunikation</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-8" name="film-platform" value="Messen">
                    <label for="film-platform-8">Messen / Events</label>
                  </div>
                  <div class="checkbox-item">
                    <input type="checkbox" id="film-platform-9" name="film-platform" value="Unklar">
                    <label for="film-platform-9">Noch unklar</label>
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 6: Länge -->
          <div class="form-step" id="step-6">
            <div class="step-content">
              <h2 class="step-title">Wie lang soll der Film ungefähr sein?</h2>
              <p class="step-description">Wählen Sie eine Option aus.</p>

              <div class="form-group">
                <div class="radio-group">
                  <div class="radio-item">
                    <input type="radio" id="film-length-1" name="film-length" value="15-30">
                    <label for="film-length-1">15–30 Sekunden</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="film-length-2" name="film-length" value="30-60">
                    <label for="film-length-2">30–60 Sekunden</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="film-length-3" name="film-length" value="1-2">
                    <label for="film-length-3">1–2 Minuten</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="film-length-4" name="film-length" value="2-5">
                    <label for="film-length-4">2–5 Minuten</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="film-length-5" name="film-length" value="Unklar">
                    <label for="film-length-5">Noch unklar</label>
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 7: Aufwand -->
          <div class="form-step" id="step-7">
            <div class="step-content">
              <h2 class="step-title">Wie aufwendig darf die Produktion sein?</h2>
              <p class="step-description">Wählen Sie eine Option aus.</p>

              <div class="form-group">
                <div class="radio-group">
                  <div class="radio-item">
                    <input type="radio" id="production-complexity-1" name="production-complexity" value="Einfach">
                    <label for="production-complexity-1">Einfach (kleines Team, wenige Drehorte)</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="production-complexity-2" name="production-complexity" value="Mittel">
                    <label for="production-complexity-2">Mittel (z. B. 1 Drehtag, mehrere Locations)</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="production-complexity-3" name="production-complexity" value="Aufwendig">
                    <label for="production-complexity-3">Aufwendig (z. B. Schauspieler, Studio, Animation)</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="production-complexity-4" name="production-complexity" value="Unklar">
                    <label for="production-complexity-4">Ich weiß es noch nicht genau</label>
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 8: Budget -->
          <div class="form-step" id="step-8">
            <div class="step-content">
              <h2 class="step-title">Gibt es ein konkretes Budgetrahmen?</h2>
              <p class="step-description">Wählen Sie eine Option aus.</p>

              <div class="form-group">
                <div class="radio-group">
                  <div class="radio-item">
                    <input type="radio" id="budget-1" name="budget" value="Unter 3.000">
                    <label for="budget-1">Unter 3.000 €</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="budget-2" name="budget" value="3.000-7.000">
                    <label for="budget-2">3.000–7.000 €</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="budget-3" name="budget" value="7.000-15.000">
                    <label for="budget-3">7.000–15.000 €</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="budget-4" name="budget" value="15.000+">
                    <label for="budget-4">15.000+ €</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="budget-5" name="budget" value="Unklar">
                    <label for="budget-5">Ich brauche Beratung / Noch unklar</label>
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 9: Zeitrahmen -->
          <div class="form-step" id="step-9">
            <div class="step-content">
              <h2 class="step-title">Wann soll das Projekt starten?</h2>
              <p class="step-description">Wählen Sie eine Option aus.</p>

              <div class="form-group">
                <div class="radio-group">
                  <div class="radio-item">
                    <input type="radio" id="timeline-1" name="timeline" value="Sofort">
                    <label for="timeline-1">Sofort</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="timeline-2" name="timeline" value="4-Wochen">
                    <label for="timeline-2">Innerhalb der nächsten 4 Wochen</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="timeline-3" name="timeline" value="2-3-Monate">
                    <label for="timeline-3">In 2–3 Monaten</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="timeline-4" name="timeline" value="Unklar">
                    <label for="timeline-4">Noch unklar</label>
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 10: Konzept -->
          <div class="form-step" id="step-10">
            <div class="step-content">
              <h2 class="step-title">Gibt es bereits ein Konzept oder Drehbuch?</h2>
              <p class="step-description">Wählen Sie eine Option aus.</p>

              <div class="form-group">
                <div class="radio-group">
                  <div class="radio-item">
                    <input type="radio" id="concept-1" name="concept" value="Ja">
                    <label for="concept-1">Ja, steht schon</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="concept-2" name="concept" value="Grob">
                    <label for="concept-2">Grobe Idee vorhanden</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="concept-3" name="concept" value="Nein">
                    <label for="concept-3">Nein, wir brauchen Hilfe bei der Konzeption</label>
                  </div>
                </div>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="button" class="btn btn-primary next-step">Weiter</button>
              </div>
            </div>
          </div>

          <!-- Step 11: Abschluss -->
          <div class="form-step" id="step-11">
            <div class="step-content">
              <h2 class="step-title">Vielen Dank!</h2>
              <div class="completion-message">
                <p>Basierend auf Ihren Angaben erstellen wir eine erste Einschätzung.</p>
                <p>Damit wir ein realistisches Angebot kalkulieren können, vereinbaren Sie bitte ein kurzes kostenloses Beratungsgespräch.</p>
                <p class="highlight">Wir kontaktieren Sie per E-Mail.</p>
              </div>

              <div class="form-buttons">
                <button type="button" class="btn btn-outline prev-step">Zurück</button>
                <button type="submit" class="btn btn-primary">Anfrage absenden</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </section>
  </div>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <div>
        <div class="footer-logo">
          <img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
        </div>
        <p class="footer-description">Professionelle Videoproduktion mit Herz und Seele. Wir bringen Ihre Botschaft zum Leben.</p>
        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
          <a href="https://www.linkedin.com/in/malte-strömsdörfer-998b55112" class="social-link" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
      <div>
        <h3 class="footer-title">Leistungen</h3>
        <ul class="footer-links">
          <li><a href="#">Werbefilme</a></li>
          <li><a href="#">Imagefilme</a></li>
          <li><a href="#">Recruitingfilme</a></li>
          <li><a href="#">Eventfilme</a></li>
          <li><a href="#">Dokumentarfilme</a></li>
        </ul>
      </div>
      <div>
        <h3 class="footer-title">Kontakt</h3>
        <ul class="footer-links">
          <li><i class="fas fa-phone mr-2"></i> <a href="tel:+4917650200617">+49 (0) 176 50200617</a></li>
          <li><i class="fas fa-envelope mr-2"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        </ul>      </div>
    </div>
    <div class="copyright">
      &copy; 2024 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.
    </div>
  </footer>

  <!-- Scripts -->
  <script src="js/app.js"></script>
  <script src="js/form.js"></script>
</body>

</html>
