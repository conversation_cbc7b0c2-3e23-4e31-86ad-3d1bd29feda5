document.addEventListener('DOMContentLoaded', function() {
  // Get all form steps
  const formSteps = document.querySelectorAll('.form-step');
  const prevButtons = document.querySelectorAll('.prev-step');
  const nextButtons = document.querySelectorAll('.next-step');
  const form = document.getElementById('film-calculator');
  const progressFill = document.querySelector('.progress-fill');
  const progressPercentage = document.querySelector('.progress-percentage');

  // Total number of steps
  const totalSteps = formSteps.length;

  // Initialize current step
  let currentStep = 0;

  // Collect form data into an object
  function collectFormData() {
    const formData = new FormData(form);
    const formDataObj = {};

    formData.forEach((value, key) => {
      // Handle multiple checkboxes
      if (formDataObj[key]) {
        if (Array.isArray(formDataObj[key])) {
          formDataObj[key].push(value);
        } else {
          formDataObj[key] = [formDataObj[key], value];
        }
      } else {
        formDataObj[key] = value;
      }
    });

    return formDataObj;
  }

  // Send form data by email
  function sendFormDataByEmail() {
    const formDataObj = collectFormData();
    const name = formDataObj.name || 'Kein Name angegeben';
    const company = formDataObj.company || 'Keine Firma angegeben';
    const email = formDataObj.email || 'Keine E-Mail angegeben';
    const phone = formDataObj.phone || 'Keine Telefonnummer angegeben';

    console.log('Sending <NAME_EMAIL>');

    // Send <NAME_EMAIL>
    const emailData = new FormData();
    emailData.append('form_type', 'calculator');
    emailData.append('form_data', JSON.stringify(formDataObj));
    emailData.append('website', ''); // Honeypot field

    // Send the data to the server
    fetch('/send_form.php', {
      method: 'POST',
      body: emailData
    })
    .then(response => response.json())
    .then(data => {
      console.log('Email sent:', data);
      return true;
    })
    .catch(error => {
      console.error('Error sending email:', error);
      // Return true anyway to ensure the user experience is not affected
      return true;
    });

    // Return true to continue the form process
    // The actual email sending happens asynchronously
    return true;
  }

  // Update progress bar
  function updateProgress(stepIndex) {
    // Calculate percentage (first step is 0%, last step is 100%)
    let percentage = 0;

    if (stepIndex === 0) {
      percentage = 0;
    } else {
      percentage = Math.round((stepIndex / (totalSteps - 1)) * 100);
    }

    // Update progress bar width
    progressFill.style.width = percentage + '%';

    // Update percentage text
    progressPercentage.textContent = percentage + '%';

    // We'll handle the 90% email sending in the next button click handler instead
    // This ensures it happens when transitioning to the final step
  }

  // Show the current step
  function showStep(stepIndex) {
    // Hide all steps
    formSteps.forEach(step => {
      step.classList.remove('active');
    });

    // Show the current step
    formSteps[stepIndex].classList.add('active');

    // Update progress bar
    updateProgress(stepIndex);

    // Scroll to top of form
    document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
  }

  // Validate the current step
  function validateStep(stepIndex) {
    // Step 1 (Intro) doesn't need validation
    if (stepIndex === 0) return true;

    // Step 2 (Contact info)
    if (stepIndex === 1) {
      const name = document.getElementById('name').value;
      const email = document.getElementById('email').value;

      if (!name) {
        alert('Bitte geben Sie Ihren Namen ein.');
        return false;
      }

      if (!email) {
        alert('Bitte geben Sie Ihre E-Mail-Adresse ein.');
        return false;
      }

      // Simple email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        alert('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
        return false;
      }

      return true;
    }

    // Step 3 (Filmart)
    if (stepIndex === 2) {
      const filmTypes = document.querySelectorAll('input[name="film-type"]:checked');
      if (filmTypes.length === 0) {
        alert('Bitte wählen Sie mindestens eine Filmart aus.');
        return false;
      }

      return true;
    }

    // Step 4 (Filmziel)
    if (stepIndex === 3) {
      const filmGoals = document.querySelectorAll('input[name="film-goal"]:checked');
      if (filmGoals.length === 0) {
        alert('Bitte wählen Sie mindestens ein Filmziel aus.');
        return false;
      }

      return true;
    }

    // Step 5 (Plattform)
    if (stepIndex === 4) {
      const platforms = document.querySelectorAll('input[name="film-platform"]:checked');
      if (platforms.length === 0) {
        alert('Bitte wählen Sie mindestens eine Plattform aus.');
        return false;
      }

      return true;
    }

    // Step 6 (Länge)
    if (stepIndex === 5) {
      const filmLength = document.querySelector('input[name="film-length"]:checked');
      if (!filmLength) {
        alert('Bitte wählen Sie eine Filmlänge aus.');
        return false;
      }

      return true;
    }

    // Step 7 (Aufwand)
    if (stepIndex === 6) {
      const complexity = document.querySelector('input[name="production-complexity"]:checked');
      if (!complexity) {
        alert('Bitte wählen Sie eine Option für den Produktionsaufwand aus.');
        return false;
      }

      return true;
    }

    // Step 8 (Budget)
    if (stepIndex === 7) {
      const budget = document.querySelector('input[name="budget"]:checked');
      if (!budget) {
        alert('Bitte wählen Sie eine Budgetoption aus.');
        return false;
      }

      return true;
    }

    // Step 9 (Zeitrahmen)
    if (stepIndex === 8) {
      const timeline = document.querySelector('input[name="timeline"]:checked');
      if (!timeline) {
        alert('Bitte wählen Sie einen Zeitrahmen aus.');
        return false;
      }

      return true;
    }

    // Step 10 (Konzept)
    if (stepIndex === 9) {
      const concept = document.querySelector('input[name="concept"]:checked');
      if (!concept) {
        alert('Bitte wählen Sie eine Option zum Konzeptstatus aus.');
        return false;
      }

      return true;
    }

    return true;
  }

  // Handle next button click
  nextButtons.forEach(button => {
    button.addEventListener('click', function() {
      if (validateStep(currentStep)) {
        // Check if we're moving from the second-to-last step to the last step (90% to 100%)
        if (currentStep === totalSteps - 2) {
          // Send the form data by email
          if (sendFormDataByEmail()) {
            // Add a small notification below the progress bar
            const notification = document.createElement('div');
            notification.className = 'email-notification';
            notification.innerHTML = '<i class="fas fa-check-circle"></i> Ihre Daten wurden an unser Team gesendet.';

            // Find the progress container and append the notification
            const progressContainer = document.querySelector('.progress-container');

            // Remove any existing notification first
            const existingNotification = document.querySelector('.email-notification');
            if (existingNotification) {
              existingNotification.remove();
            }

            progressContainer.appendChild(notification);

            // Fade out the notification after 5 seconds
            setTimeout(() => {
              notification.style.opacity = '0';
              setTimeout(() => {
                notification.remove();
              }, 1000);
            }, 5000);
          }
        }

        // Move to the next step
        currentStep++;
        showStep(currentStep);
      }
    });
  });

  // Handle previous button click
  prevButtons.forEach(button => {
    button.addEventListener('click', function() {
      currentStep--;
      showStep(currentStep);
    });
  });

  // Handle form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();

    // Ensure progress is at 100%
    progressFill.style.width = '100%';
    progressPercentage.textContent = '100%';

    // Show success message
    setTimeout(function() {
      alert('Vielen Dank für Ihre Anfrage! Wir werden uns in Kürze per E-Mail bei Ihnen melden.');
    }, 500);

    // Optional: Reset form
    // form.reset();
    // currentStep = 0;
    // showStep(currentStep);
  });

  // Initialize the form
  showStep(currentStep);
});
