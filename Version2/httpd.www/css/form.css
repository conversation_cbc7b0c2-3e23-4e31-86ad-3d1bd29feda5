/* Form Styles for Contact and Meeting Forms */

/* Form Header Styles */
.form-header {
  text-align: center;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-form-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.main-form-subtitle {
  font-size: 1.2rem;
  color: var(--text-gray);
  margin-bottom: 0;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .main-form-title {
    font-size: 2rem;
  }

  .main-form-subtitle {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .main-form-title {
    font-size: 1.8rem;
  }

  .main-form-subtitle {
    font-size: 1rem;
  }
}

/* Contact Form Styles */
.contact-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-light);
}

.form-group input,
.form-group textarea {
  padding: 0.8rem 1rem;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  font-family: inherit;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: rgba(255, 255, 255, 0.08);
}

.checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.checkbox-container input[type="checkbox"] {
  margin-top: 0.2rem;
  flex-shrink: 0;
}

.checkbox-container label {
  margin-bottom: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Meeting Form Styles */
.form-section {
  padding: 8rem 0 6rem;
  min-height: calc(100vh - 80px);
  width: 100%;
}

.form-container {
  max-width: 900px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Progress Indicator */
.progress-container {
  margin-bottom: 3rem;
  text-align: center;
  padding: 1rem 0;
}

.progress-percentage {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.8rem;
  letter-spacing: 0.05em;
}

.progress-bar {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  max-width: 80%;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  background-image: linear-gradient(to right, #ff3030, #ff5050);
  border-radius: 5px;
  width: 0%;
  transition: width 0.4s ease;
}

/* Multi-step Form */
.multi-step-form {
  position: relative;
}

.form-step {
  display: none;
  animation: fadeIn 0.5s ease;
}

.form-step.active {
  display: block;
}

.step-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.step-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.step-description {
  font-size: 1.1rem;
  color: var(--text-gray);
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

/* Form Elements */
.form-group {
  margin-bottom: 2rem;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 600;
  color: var(--text-light);
  font-size: 1.1rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 1rem 1.2rem;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(255, 48, 48, 0.1);
}

/* Radio and Checkbox Groups */
.radio-group,
.checkbox-group {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.radio-item,
.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.radio-item:hover,
.checkbox-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.radio-item input[type="radio"],
.checkbox-item input[type="checkbox"] {
  width: auto;
  margin: 0;
  accent-color: var(--primary-color);
}

.radio-item label,
.checkbox-item label {
  margin: 0;
  cursor: pointer;
  font-weight: 500;
}

/* Time Slots */
.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 1.5rem;
}

.time-slot-morning,
.time-slot-afternoon,
.time-slot-evening {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.time-slot-morning h4,
.time-slot-afternoon h4,
.time-slot-evening h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Form Buttons */
.form-buttons {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 3rem;
}

.form-buttons.text-center {
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-form {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .form-container {
    padding: 2rem;
    margin: 1rem;
  }
  
  .step-title {
    font-size: 1.8rem;
  }
  
  .time-slots {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .form-buttons {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .form-container {
    padding: 1.5rem;
  }
  
  .step-title {
    font-size: 1.6rem;
  }
  
  .progress-percentage {
    font-size: 1.5rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
